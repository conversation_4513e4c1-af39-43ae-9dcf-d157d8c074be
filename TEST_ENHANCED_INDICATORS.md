# Testing Enhanced Indicators

## Quick Test Guide

### 1. Basic Functionality Test

1. **Load the plugin**
   - Start FFXIV with <PERSON><PERSON><PERSON>
   - Ensure the plugin loads without errors
   - Check <PERSON><PERSON><PERSON> log for any initialization issues

2. **Test Traditional Indicators**
   - Open config: `/pvplines config`
   - Go to Appearance tab
   - Ensure "Use Enhanced 3D Rendering" is **unchecked**
   - Try different traditional indicator types (Lines, Outlines, etc.)
   - Verify they work as before

3. **Test Enhanced Indicators**
   - Check "Use Enhanced 3D Rendering (Splatoon)"
   - Select "3D World Circles" from dropdown
   - Enter a PvP zone or disable "Only in PvP" setting
   - Look for enhanced circular indicators around enemies

### 2. Configuration Test

1. **Color Settings**
   - Change Enemy Marker Color - should affect indicator colors
   - Change Ally Marker Color - should affect ally indicators
   - Test with both enemies and allies visible

2. **Size Settings**
   - Adjust Marker Radius (0.5-10.0) - should change indicator size
   - Adjust Marker Height (0.0-5.0) - should affect positioning

3. **Performance Settings**
   - Set Max Elements to 10 - should limit number of indicators
   - Enable "Limit by Distance" - should cull distant indicators
   - Adjust Max Render Distance - should affect render range

### 3. Indicator Type Tests

Test each enhanced indicator type:

- **3D World Circles** - Should show concentric circles
- **3D World Markers** - Should show diamond shapes
- **VFX Omens** - Should show rotating segments
- **VFX Tethers** - Should show animated beams with particles
- **Ground Markers** - Should show cross patterns
- **3D Beams** - Should show gradient beam lines
- **Directional Cones** - Should show cone shapes
- **3D Spheres** - Should show multi-layered circles
- **Hybrid options** - Should combine multiple effects

### 4. Error Testing

1. **Check Dalamud Log**
   - Look for "Error in enhanced indicator rendering" messages
   - Check for ECommons initialization errors
   - Verify configuration migration completed

2. **Performance Testing**
   - Test with many enemies (Frontlines)
   - Monitor FPS impact
   - Adjust settings if performance issues occur

### 5. Migration Testing

1. **Existing Users**
   - Existing settings should be preserved
   - Traditional indicators should work unchanged
   - Enhanced rendering should be disabled by default

2. **New Users**
   - Default settings should be reasonable
   - All indicator types should be accessible
   - No errors on first run

## Expected Results

### Working Correctly
- ✅ Plugin loads without errors
- ✅ Traditional indicators work as before
- ✅ Enhanced indicators show visual effects
- ✅ Configuration changes take effect immediately
- ✅ Performance is acceptable (>30 FPS)
- ✅ No error messages in Dalamud log

### Common Issues
- ❌ "Enhanced rendering not working" - Check if enabled in config
- ❌ "No indicators visible" - Check PvP zone requirement
- ❌ "Performance issues" - Reduce max elements or render distance
- ❌ "Visual artifacts" - Adjust marker radius/height settings

## Troubleshooting Commands

```
/pvplines config          # Open configuration
/xllog                    # Open Dalamud log
/xlplugins                # Check plugin status
```

## Test Scenarios

### Scenario 1: Frontlines (Large Scale)
- Many enemies and allies
- Test performance with high player count
- Verify distance culling works

### Scenario 2: Crystalline Conflict (Small Scale)
- Close-quarters combat
- Test different indicator types
- Verify visual clarity

### Scenario 3: Wolves' Den (Dueling)
- 1v1 scenarios
- Test precision of indicators
- Verify low-latency rendering

## Performance Benchmarks

### Acceptable Performance
- **60+ FPS**: Excellent
- **30-60 FPS**: Good
- **Below 30 FPS**: Needs optimization

### Optimization Steps
1. Reduce Max Elements (50 → 25 → 10)
2. Enable Distance Limiting
3. Reduce Max Render Distance (100 → 75 → 50)
4. Switch to simpler indicator types
5. Disable enhanced rendering if needed

## Success Criteria

The enhanced indicator system is working correctly if:

1. ✅ All traditional indicators continue to work
2. ✅ Enhanced indicators provide visual improvements
3. ✅ Configuration system is intuitive and responsive
4. ✅ Performance impact is minimal (<10% FPS loss)
5. ✅ No crashes or errors occur during normal use
6. ✅ Settings persist between game sessions
7. ✅ Migration from old version works seamlessly

## Reporting Issues

When reporting issues, please include:

1. **System Information**
   - GPU model and drivers
   - Game resolution and settings
   - Other Dalamud plugins installed

2. **Configuration Details**
   - Enhanced rendering enabled/disabled
   - Specific indicator type being used
   - Performance settings (max elements, distance limits)

3. **Error Information**
   - Exact error messages from Dalamud log
   - Steps to reproduce the issue
   - Screenshots or video if visual issue

4. **Performance Data**
   - FPS before and after enabling enhanced rendering
   - Number of players in area when issue occurs
   - System resource usage (CPU/GPU/RAM)
