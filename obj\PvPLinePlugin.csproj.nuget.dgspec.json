{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj", "projectName": "PvPLinePlugin", "projectPath": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"DalamudPackager": {"target": "Package", "version": "[2.1.14, )"}, "ECommons": {"target": "Package", "version": "[3.0.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}