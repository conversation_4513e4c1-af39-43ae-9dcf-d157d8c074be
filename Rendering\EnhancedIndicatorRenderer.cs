using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;
using PvPLinePlugin.Utils;

namespace PvPLinePlugin.Rendering;

public class EnhancedIndicatorRenderer : IDisposable
{
    private readonly Configuration config;

    public EnhancedIndicatorRenderer(Configuration configuration)
    {
        config = configuration;
    }

    public void Dispose()
    {
        // Enhanced 2D rendering doesn't require cleanup
        GC.SuppressFinalize(this);
    }

    public void RenderIndicators(IPlayerCharacter localPlayer, List<IPlayerCharacter> enemies, List<IPlayerCharacter> allies)
    {
        if (!config.UseSplatoonRendering)
            return;

        try
        {
            // For now, use enhanced 2D rendering until Splatoon integration is fully implemented
            var drawList = ImGui.GetBackgroundDrawList();

            // Render enemy indicators
            if (config.ShowEnemies)
            {
                foreach (var enemy in enemies)
                {
                    RenderEnhanced2DIndicator(drawList, localPlayer, enemy, false);
                }
            }

            // Render ally indicators
            if (config.ShowAllies)
            {
                foreach (var ally in allies)
                {
                    RenderEnhanced2DIndicator(drawList, localPlayer, ally, true);
                }
            }
        }
        catch (Exception ex)
        {
            Plugin.Log.Error($"Error in enhanced indicator rendering: {ex.Message}");
        }
    }

    private void RenderEnhanced2DIndicator(ImDrawListPtr drawList, IPlayerCharacter localPlayer, IPlayerCharacter target, bool isAlly)
    {
        if (target == null || localPlayer == null)
            return;

        var distance = Vector3.Distance(localPlayer.Position, target.Position);
        if (config.SplatoonLimitByDistance && distance > config.SplatoonMaxRenderDistance)
            return;

        // Convert world positions to screen positions
        if (!Plugin.GameGui.WorldToScreen(target.Position, out var targetScreen) ||
            !Plugin.GameGui.WorldToScreen(localPlayer.Position, out var playerScreen))
            return;

        switch (config.IndicatorType)
        {
            // Traditional indicator types (fallback to enhanced 2D rendering)
            case IndicatorType.Lines:
                RenderTraditionalLine(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.Outlines:
                RenderTraditionalLine(drawList, playerScreen, targetScreen, isAlly); // Fallback to lines
                break;
            case IndicatorType.Nameplates:
                RenderTraditionalNameplate(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.Icons:
                RenderTraditionalIcon(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.DirectionalArrows:
                RenderTraditionalArrow(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.HealthBars:
                RenderTraditionalHealthBar(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.Combination:
                RenderTraditionalCombination(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.ScreenEdgeArrows:
                RenderTraditionalScreenEdgeArrow(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.GradientLines:
                RenderTraditionalGradientLine(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.AnimatedPulse:
                RenderTraditionalAnimatedPulse(drawList, playerScreen, targetScreen, isAlly);
                break;

            // Enhanced 3D indicator types
            case IndicatorType.SplatoonWorldCircles:
                RenderEnhancedWorldCircle(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonWorldMarkers:
                RenderEnhancedWorldMarker(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonVfxOmens:
                RenderEnhancedVfxOmen(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonVfxTethers:
                RenderEnhancedVfxTether(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonGroundMarkers:
                RenderEnhancedGroundMarker(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonBeams:
                RenderEnhancedBeam(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonCones:
                RenderEnhancedCone(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonSpheres:
                RenderEnhancedSphere(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonFloatingIcons:
                RenderEnhancedFloatingIcon(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonCustomShapes:
                RenderEnhancedCustomShape(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.HybridLinesAndMarkers:
                RenderHybridLinesAndMarkers(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.HybridIconsAndBeams:
                RenderHybridIconsAndBeams(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.HybridAdvancedCombo:
                RenderHybridAdvancedCombo(drawList, playerScreen, targetScreen, isAlly);
                break;
        }
    }

    private void RenderEnhancedWorldCircle(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonWorldMarkersEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 20; // Scale for screen space

        if (config.SplatoonShapesFillInside)
        {
            drawList.AddCircleFilled(targetScreen, radius, ColorUtils.WithAlpha(color, 0.3f));
        }

        // Draw multiple concentric circles for enhanced 3D effect
        drawList.AddConcentricCircles(targetScreen, radius, color, 3, 5.0f, 0.2f);
    }

    private void RenderEnhancedWorldMarker(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonWorldMarkersEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var size = config.SplatoonMarkerRadius * 15; // Scale for screen space

        // Draw diamond-shaped marker
        var points = new Vector2[]
        {
            new(targetScreen.X, targetScreen.Y - size),      // Top
            new(targetScreen.X + size, targetScreen.Y),      // Right
            new(targetScreen.X, targetScreen.Y + size),      // Bottom
            new(targetScreen.X - size, targetScreen.Y)       // Left
        };

        // Fill
        drawList.AddConvexPolyFilled(ref points[0], 4, ColorUtils.WithAlpha(color, 0.6f));

        // Border
        for (int i = 0; i < 4; i++)
        {
            drawList.AddLine(points[i], points[(i + 1) % 4], color, 2.0f);
        }
    }

    private void RenderEnhancedVfxOmen(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 25; // Scale for omen effect

        // Draw omen-style rotating segments (works with or without VFX enabled)
        var time = (float)ImGui.GetTime();
        var segments = 8;
        var segmentAngle = (float)(2 * Math.PI / segments);

        for (int i = 0; i < segments; i++)
        {
            var angle = time * 2.0f + i * segmentAngle;
            var innerRadius = radius * 0.7f;
            var outerRadius = radius;

            var innerPoint = targetScreen + new Vector2(
                (float)Math.Cos(angle) * innerRadius,
                (float)Math.Sin(angle) * innerRadius
            );
            var outerPoint = targetScreen + new Vector2(
                (float)Math.Cos(angle) * outerRadius,
                (float)Math.Sin(angle) * outerRadius
            );

            var alpha = (float)(0.3f + 0.7f * Math.Sin(time * 3.0f + i * 0.5f));
            drawList.AddLine(innerPoint, outerPoint, ColorUtils.WithAlpha(color, alpha), 3.0f);
        }

        // Add center circle for better visibility
        drawList.AddCircle(targetScreen, radius * 0.3f, color, 0, 2.0f);
    }

    private void RenderEnhancedVfxTether(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var thickness = config.SplatoonBeamThickness * 10; // Scale for screen space

        // Draw animated tether with pulsing effect (works with or without VFX enabled)
        var time = (float)ImGui.GetTime();
        var pulse = (float)(0.7f + 0.3f * Math.Sin(time * 4.0f));

        // Main tether line
        drawList.AddLine(playerScreen, targetScreen, ColorUtils.WithAlpha(color, pulse), thickness);

        // Add energy particles along the line
        var direction = Vector2.Normalize(targetScreen - playerScreen);
        var distance = Vector2.Distance(playerScreen, targetScreen);
        var particleCount = (int)(distance / 20);

        for (int i = 0; i < particleCount; i++)
        {
            var t = (float)i / particleCount;
            var particlePos = Vector2.Lerp(playerScreen, targetScreen, t);
            var particleOffset = time * 100.0f + i * 20.0f;
            particlePos += direction * (particleOffset % 40.0f - 20.0f);

            drawList.AddCircleFilled(particlePos, 2.0f, ColorUtils.WithAlpha(color, pulse * 0.8f));
        }

        // Add glow effect around the line
        drawList.AddLine(playerScreen, targetScreen, ColorUtils.WithAlpha(color, pulse * 0.3f), thickness * 2);
    }

    private void RenderEnhancedGroundMarker(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 15; // Scale for screen space

        // Draw ground marker with cross pattern
        drawList.AddCircleFilled(targetScreen, radius, ColorUtils.WithAlpha(color, 0.4f));
        drawList.AddCircle(targetScreen, radius, color, 0, 3.0f);

        // Add cross in the center
        var crossSize = radius * 0.6f;
        drawList.AddLine(
            new Vector2(targetScreen.X - crossSize, targetScreen.Y),
            new Vector2(targetScreen.X + crossSize, targetScreen.Y),
            color, 3.0f);
        drawList.AddLine(
            new Vector2(targetScreen.X, targetScreen.Y - crossSize),
            new Vector2(targetScreen.X, targetScreen.Y + crossSize),
            color, 3.0f);
    }

    private void RenderEnhancedBeam(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonBeamsEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonBeamColor;
        var thickness = config.SplatoonBeamThickness * 8; // Scale for screen space

        // Draw main beam with gradient effect
        drawList.AddGradientLine(playerScreen, targetScreen,
            ColorUtils.WithAlpha(color, 0.8f),
            ColorUtils.WithAlpha(color, 0.3f),
            thickness);

        // Add core beam
        drawList.AddLine(playerScreen, targetScreen, color, thickness * 0.3f);
    }

    private void RenderEnhancedCone(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var direction = Vector2.Normalize(targetScreen - playerScreen);
        var distance = Vector2.Distance(playerScreen, targetScreen);

        // Make cone more visible with minimum width
        var coneWidth = Math.Max(distance * 0.3f, 30.0f); // Minimum 30 pixels wide
        coneWidth = Math.Min(coneWidth, config.SplatoonMarkerRadius * 30); // Scale with marker radius

        // Calculate cone points
        var perpendicular = new Vector2(-direction.Y, direction.X);
        var coneBase1 = targetScreen + perpendicular * coneWidth;
        var coneBase2 = targetScreen - perpendicular * coneWidth;

        // Draw filled cone using triangles (more reliable than AddConvexPolyFilled)
        drawList.AddTriangleFilled(playerScreen, coneBase1, coneBase2, ColorUtils.WithAlpha(color, 0.4f));

        // Draw cone outline with thicker lines for better visibility
        drawList.AddLine(playerScreen, coneBase1, color, 3.0f);
        drawList.AddLine(playerScreen, coneBase2, color, 3.0f);
        drawList.AddLine(coneBase1, coneBase2, color, 3.0f);

        // Add center line for direction indication
        drawList.AddLine(playerScreen, targetScreen, ColorUtils.WithAlpha(color, 0.8f), 2.0f);
    }

    private void RenderEnhancedSphere(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 18; // Scale for screen space

        if (config.SplatoonShapesFillInside)
        {
            drawList.AddCircleFilled(targetScreen, radius, ColorUtils.WithAlpha(color, 0.4f));
        }

        // Draw multiple circles to simulate 3D sphere
        for (int i = 0; i < 3; i++)
        {
            var layerRadius = radius * (1.0f - i * 0.2f);
            var alpha = 1.0f - i * 0.3f;
            drawList.AddCircle(targetScreen, layerRadius, ColorUtils.WithAlpha(color, alpha), 0, 2.0f);
        }
    }

    private void RenderHybridLinesAndMarkers(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        // Render enhanced world marker
        RenderEnhancedWorldMarker(drawList, targetScreen, isAlly);

        // Add traditional line
        var color = isAlly ? config.AllyLineColor.ToImGuiColor() : config.LineColor.ToImGuiColor();
        drawList.AddLine(playerScreen, targetScreen, color, config.LineThickness);
    }

    private void RenderHybridIconsAndBeams(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        // Render enhanced beam
        RenderEnhancedBeam(drawList, playerScreen, targetScreen, isAlly);

        // Add floating icon marker
        RenderEnhancedWorldMarker(drawList, targetScreen, isAlly);
    }

    private void RenderEnhancedFloatingIcon(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var iconSize = config.SplatoonMarkerRadius * 15; // Scale for screen space

        // Draw floating icon above target position
        var iconPos = new Vector2(targetScreen.X, targetScreen.Y - 30);

        // Draw icon background circle
        drawList.AddCircleFilled(iconPos, iconSize / 2, ColorUtils.WithAlpha(color, 0.8f));
        drawList.AddCircle(iconPos, iconSize / 2, color, 0, 2.0f);

        // Add center dot
        drawList.AddCircleFilled(iconPos, 3.0f, ColorUtils.WithAlpha(color, 1.0f));
    }

    private void RenderEnhancedCustomShape(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var shapeSize = config.SplatoonShapeScale * 20; // Scale for screen space

        // Draw custom diamond shape
        var points = new Vector2[]
        {
            new Vector2(targetScreen.X, targetScreen.Y - shapeSize), // Top
            new Vector2(targetScreen.X + shapeSize, targetScreen.Y), // Right
            new Vector2(targetScreen.X, targetScreen.Y + shapeSize), // Bottom
            new Vector2(targetScreen.X - shapeSize, targetScreen.Y)  // Left
        };

        // Draw shape outline
        for (int i = 0; i < points.Length; i++)
        {
            var nextIndex = (i + 1) % points.Length;
            drawList.AddLine(points[i], points[nextIndex], color, 2.0f);
        }

        // Fill if enabled
        if (config.SplatoonShapesFillInside)
        {
            // Draw filled diamond using triangles
            var fillColor = ColorUtils.WithAlpha(color, config.SplatoonShapeOpacity);
            drawList.AddTriangleFilled(points[0], points[1], targetScreen, fillColor);
            drawList.AddTriangleFilled(points[1], points[2], targetScreen, fillColor);
            drawList.AddTriangleFilled(points[2], points[3], targetScreen, fillColor);
            drawList.AddTriangleFilled(points[3], points[0], targetScreen, fillColor);
        }
    }

    private void RenderHybridAdvancedCombo(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        // Advanced combination of multiple effects
        RenderEnhancedWorldMarker(drawList, targetScreen, isAlly);
        RenderEnhancedFloatingIcon(drawList, targetScreen, isAlly);

        // Add traditional line
        var lineColor = isAlly ? config.AllyLineColor.ToImGuiColor() : config.LineColor.ToImGuiColor();
        drawList.AddLine(playerScreen, targetScreen, lineColor, config.LineThickness);

        // Add pulsing effect for extra visibility
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var time = (float)ImGui.GetTime();
        var pulse = (float)(Math.Sin(time * 2.0f) * 0.3f + 0.7f);

        drawList.AddCircle(targetScreen, 25.0f, ColorUtils.WithAlpha(color, pulse), 0, 3.0f);
    }

    // Traditional indicator rendering methods (for when enhanced 3D rendering is enabled)
    private void RenderTraditionalLine(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var lineColor = isAlly ? config.AllyLineColor : config.LineColor;
        var color = lineColor.ToImGuiColor();
        var thickness = isAlly ? config.AllyLineThickness : config.LineThickness;

        if (config.UseDashedLines)
        {
            drawList.AddDashedLine(playerScreen, targetScreen, color, thickness, config.DashLength, config.GapLength);
        }
        else
        {
            drawList.AddLine(playerScreen, targetScreen, color, thickness);
        }
    }

    private void RenderTraditionalNameplate(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var lineColor = isAlly ? config.AllyLineColor : config.LineColor;
        var color = lineColor.ToImGuiColor();

        // Draw a colored rectangle as nameplate
        var size = new Vector2(80, 20);
        var rectMin = new Vector2(targetScreen.X - size.X / 2, targetScreen.Y - 30);
        var rectMax = new Vector2(rectMin.X + size.X, rectMin.Y + size.Y);

        var bgColor = new Vector4(0, 0, 0, 0.7f);
        drawList.AddRectFilled(rectMin, rectMax, bgColor.ToImGuiColor());
        drawList.AddRect(rectMin, rectMax, color, 0, ImDrawFlags.None, 2);

        // Add text
        var text = isAlly ? "ALLY" : "ENEMY";
        var textSize = ImGui.CalcTextSize(text);
        var textPos = new Vector2(rectMin.X + (size.X - textSize.X) / 2, rectMin.Y + (size.Y - textSize.Y) / 2);
        drawList.AddText(textPos, color, text);
    }

    private void RenderTraditionalIcon(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var lineColor = isAlly ? config.AllyLineColor : config.LineColor;
        var color = lineColor.ToImGuiColor();

        // Draw a simple circle icon
        var radius = config.IconSize / 2;
        drawList.AddCircleFilled(targetScreen, radius, color);
        drawList.AddCircle(targetScreen, radius, ColorUtils.ToImGuiColor(Vector4.One), 0, 2);

        // Add center dot
        drawList.AddCircleFilled(targetScreen, 3.0f, ColorUtils.ToImGuiColor(Vector4.One));
    }

    private void RenderTraditionalArrow(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var lineColor = isAlly ? config.AllyLineColor : config.LineColor;
        var color = lineColor.ToImGuiColor();
        var thickness = isAlly ? config.AllyLineThickness : config.LineThickness;

        drawList.AddArrow(playerScreen, targetScreen, color, thickness, config.ArrowSize);
    }

    private void RenderTraditionalHealthBar(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var lineColor = isAlly ? config.AllyLineColor : config.LineColor;
        var color = lineColor.ToImGuiColor();

        // Draw health bar above target
        var barWidth = 60f;
        var barHeight = 8f;
        var barPos = new Vector2(targetScreen.X - barWidth / 2, targetScreen.Y - 40);

        // Background
        var bgColor = new Vector4(0, 0, 0, 0.7f);
        drawList.AddRectFilled(barPos, new Vector2(barPos.X + barWidth, barPos.Y + barHeight), bgColor.ToImGuiColor());

        // Health fill (assume 75% health for demo)
        var healthPercent = 0.75f;
        var healthWidth = barWidth * healthPercent;
        drawList.AddRectFilled(barPos, new Vector2(barPos.X + healthWidth, barPos.Y + barHeight), color);

        // Border
        drawList.AddRect(barPos, new Vector2(barPos.X + barWidth, barPos.Y + barHeight), ColorUtils.ToImGuiColor(Vector4.One), 0, ImDrawFlags.None, 1);
    }

    private void RenderTraditionalCombination(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        // Combine line and arrow
        RenderTraditionalLine(drawList, playerScreen, targetScreen, isAlly);
        if (config.ShowDirectionalArrows)
        {
            RenderTraditionalArrow(drawList, playerScreen, targetScreen, isAlly);
        }
    }

    private void RenderTraditionalScreenEdgeArrow(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var lineColor = isAlly ? config.AllyLineColor : config.LineColor;
        var color = lineColor.ToImGuiColor();

        // Calculate direction to target
        var direction = Vector2.Normalize(targetScreen - playerScreen);
        drawList.AddScreenEdgeArrow(playerScreen, direction, color, config.ArrowSize, config.ScreenEdgeMargin);
    }

    private void RenderTraditionalGradientLine(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var startColor = isAlly ? config.AllyLineColor : config.LineColor;
        var endColor = config.GradientEndColor;
        var thickness = isAlly ? config.AllyLineThickness : config.LineThickness;

        drawList.AddGradientLine(playerScreen, targetScreen, startColor.ToImGuiColor(), endColor.ToImGuiColor(), thickness);
    }

    private void RenderTraditionalAnimatedPulse(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var lineColor = isAlly ? config.AllyLineColor : config.LineColor;
        var thickness = isAlly ? config.AllyLineThickness : config.LineThickness;

        // Draw pulsing circle at target position
        drawList.AddPulsingCircleFilled(targetScreen, config.IconSize / 2, lineColor.ToImGuiColor(),
            config.PulseSpeed, config.PulseAmount);

        // Draw pulsing line
        var time = (float)ImGui.GetTime();
        var pulse = (float)(Math.Sin(time * config.PulseSpeed) * config.PulseAmount + 1.0f);
        var pulsedThickness = thickness * pulse;
        var pulsedColor = ColorUtils.WithAlpha(lineColor, lineColor.W * pulse);

        drawList.AddLine(playerScreen, targetScreen, pulsedColor.ToImGuiColor(), pulsedThickness);
    }

    // Enhanced 2D rendering doesn't need element pooling like Splatoon
    // All rendering is done directly to ImGui draw list each frame
}
